import type { PlasmoCSConfig } from 'plasmo';
import { RuntimeActionResponse } from '~/types';

export const config: PlasmoCSConfig = {
  matches: ['<all_urls>'], // Or restrict to specific domains
  run_at: 'document_idle',
};

chrome.runtime.sendMessage({ name: 'load-scripts' }, (response: RuntimeActionResponse) => {
  (response.data as string[]).forEach((script: string) => {
    const blob = new Blob([script], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);

    const s = document.createElement('script');
    s.src = url;
    s.onload = () => URL.revokeObjectURL(url); // cleanup
    (document.head || document.documentElement).appendChild(s);
  });
});

chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  (async () => {
    try {
      console.log('Received message in content script:', request);
      switch (request.name) {
        case 'inject-script': {
          const { code } = request.body as { code: string };
          console.log('Injecting script:', code);
          const blob = new Blob([code], { type: 'text/javascript' });
          const url = URL.createObjectURL(blob);
          const s = document.createElement('script');
          s.src = url;
          s.onload = () => URL.revokeObjectURL(url); // cleanup
          (document.head || document.documentElement).appendChild(s);
          sendResponse({ success: true });
          break;
        }
        default: {
          sendResponse({ success: false, error: 'Unknown request' });
          break;
        }
      }
    } catch (error) {
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
    return true;
  })();
});
